# Matrix Homeserver Podman完整部署指南（小白专用版）

## 📋 目录

1. [项目概述](#项目概述)
2. [系统要求](#系统要求)
3. [环境准备](#环境准备)
4. [Podman安装配置](#podman安装配置)
5. [项目文件准备](#项目文件准备)
6. [配置文件设置](#配置文件设置)
7. [服务部署](#服务部署)
8. [验证测试](#验证测试)
9. [故障排除](#故障排除)
10. [日常维护](#日常维护)

---

## 🎯 项目概述

### 什么是Matrix Homeserver？

Matrix Homeserver是一个去中心化的实时通信服务器，类似于自建的微信/QQ服务器。本指南将帮助您使用Podman（Docker的安全替代品）在自己的服务器上部署一个完整的Matrix服务。

### 为什么选择Podman？

✅ **更安全**：无需root权限运行容器  
✅ **更轻量**：无守护进程，资源占用更少  
✅ **更稳定**：减少单点故障风险  
✅ **兼容性好**：支持Docker镜像和大部分Docker命令  

### 部署架构

```
┌─────────────────────────────────────────────────────────────┐
│                    您的服务器                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   Nginx     │  │   Synapse   │  │  PostgreSQL │          │
│  │ (反向代理)   │  │ (Matrix核心) │  │  (数据库)   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│  ┌─────────────┐  ┌─────────────┐                           │
│  │   Coturn    │  │    Redis    │                           │
│  │ (音视频中继) │  │   (缓存)    │                           │
│  └─────────────┘  └─────────────┘                           │
└─────────────────────────────────────────────────────────────┘
```

---

## 💻 系统要求

### 硬件要求

| 组件 | 最低配置 | 推荐配置 | 说明 |
|------|----------|----------|------|
| CPU | 2核心 | 4核心+ | 影响消息处理速度 |
| 内存 | 4GB | 8GB+ | 数据库和缓存需要 |
| 存储 | 50GB SSD | 200GB+ SSD | 消息和媒体文件存储 |
| 网络 | 10Mbps | 100Mbps+ | 影响文件传输速度 |

### 软件要求

- **操作系统**：Ubuntu 20.04+ / Debian 11+ / CentOS 8+
- **架构**：x86_64 (amd64)
- **网络**：固定IP地址（内网或公网）
- **域名**：一个可以管理DNS的域名

### 网络要求

- 能够配置端口转发（如果在内网）
- 有管理DNS记录的权限
- 可以申请SSL证书

---

## 🛠️ 环境准备

### 第一步：更新系统

> 💡 **说明**：首先确保系统是最新的，避免兼容性问题

```bash
# 更新软件包列表
sudo apt update

# 升级所有已安装的软件包
sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git nano vim unzip htop \
    dnsutils net-tools iputils-ping ufw fail2ban \
    build-essential python3 python3-pip

# 重启系统以确保所有更新生效
sudo reboot
```

**预期输出**：
```
正在读取软件包列表... 完成
正在分析软件包的依赖关系树... 完成
升级了 X 个软件包，新安装了 Y 个软件包...
```

### 第二步：创建专用用户

> 🔐 **安全提醒**：不要使用root用户运行服务，创建专用用户更安全

```bash
# 创建matrix用户
sudo useradd -m -s /bin/bash matrix

# 设置强密码（请设置复杂密码）
sudo passwd matrix

# 添加到sudo组（获得管理员权限）
sudo usermod -aG sudo matrix

# 验证用户创建成功
id matrix
```

**预期输出**：
```
uid=1001(matrix) gid=1001(matrix) groups=1001(matrix),27(sudo)
```

**密码要求**：
- 至少12个字符
- 包含大小写字母、数字、特殊字符
- 示例：`Matrix2025!@Server#`（请勿直接使用）

### 第三步：配置防火墙

> 🛡️ **安全配置**：只开放必要的端口

```bash
# 启用防火墙
sudo ufw enable

# 允许SSH连接（重要：避免被锁定）
sudo ufw allow ssh

# 允许Matrix服务端口
sudo ufw allow 8448/tcp comment "Matrix HTTPS"
sudo ufw allow 3478/tcp comment "STUN/TURN TCP"
sudo ufw allow 3478/udp comment "STUN/TURN UDP"
sudo ufw allow 5349/tcp comment "TURNS"
sudo ufw allow 49152:65535/udp comment "TURN UDP range"

# 查看防火墙状态
sudo ufw status verbose
```

**预期输出**：
```
状态：激活
日志记录：开（低）
默认：拒绝（传入），允许（传出），禁用（路由）
新配置文件：跳过

至                          动作          来自
--                          --          --
22/tcp                     ALLOW IN    Anywhere
8448/tcp (Matrix HTTPS)    ALLOW IN    Anywhere
3478/tcp (STUN/TURN TCP)   ALLOW IN    Anywhere
...
```

---

## 🐳 Podman安装配置

### 第一步：安装Podman

> 📦 **说明**：Podman是Docker的安全替代品，支持无根容器

```bash
# Ubuntu/Debian系统安装Podman
sudo apt update
sudo apt install -y podman

# 验证安装
podman --version
```

**预期输出**：
```
podman version 3.4.4
```

### 第二步：安装podman-compose

> 🔧 **工具说明**：podman-compose提供类似docker-compose的功能

```bash
# 切换到matrix用户
su - matrix

# 安装podman-compose
pip3 install --user podman-compose

# 添加到PATH
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# 验证安装
podman-compose --version
```

**预期输出**：
```
podman-compose version 1.0.6
```

### 第三步：配置无根容器

> 🔒 **安全配置**：配置用户命名空间，实现无root权限运行容器

```bash
# 配置用户命名空间（需要sudo权限）
echo "matrix:100000:65536" | sudo tee -a /etc/subuid
echo "matrix:100000:65536" | sudo tee -a /etc/subgid

# 验证配置
grep matrix /etc/subuid /etc/subgid
```

**预期输出**：
```
/etc/subuid:matrix:100000:65536
/etc/subgid:matrix:100000:65536
```

### 第四步：配置Podman注册表

> 🌐 **镜像源配置**：配置容器镜像仓库

```bash
# 创建配置目录
mkdir -p ~/.config/containers

# 配置注册表
cat > ~/.config/containers/registries.conf << 'EOF'
# Podman注册表配置文件

[registries.search]
# 搜索镜像时使用的注册表列表
registries = ['docker.io', 'quay.io']

[registries.insecure]
# 不安全的注册表（HTTP）
registries = []

[registries.block]
# 阻止的注册表
registries = []

# Docker Hub配置
[[registry]]
prefix = "docker.io"
location = "docker.io"

# Quay.io配置  
[[registry]]
prefix = "quay.io"
location = "quay.io"
EOF
```

### 第五步：测试Podman安装

> ✅ **验证安装**：确保Podman工作正常

```bash
# 测试基本功能
podman run --rm hello-world

# 测试镜像拉取
podman pull nginx:alpine

# 测试容器运行
podman run -d --name test-nginx -p 8080:80 nginx:alpine

# 检查运行状态
podman ps

# 清理测试容器
podman stop test-nginx
podman rm test-nginx
podman rmi nginx:alpine
```

**预期输出**：
```
CONTAINER ID  IMAGE                           COMMAND               CREATED        STATUS            PORTS                 NAMES
abc123def456  docker.io/library/nginx:alpine  nginx -g daemon o...  2 seconds ago  Up 1 second ago  0.0.0.0:8080->80/tcp  test-nginx
```

---

## 📁 项目文件准备

### 第一步：创建项目目录

> 📂 **目录结构**：创建规范的项目目录结构

```bash
# 确保在matrix用户下操作
whoami  # 应该显示 matrix

# 创建项目根目录
sudo mkdir -p /opt/matrix
sudo chown matrix:matrix /opt/matrix

# 进入项目目录
cd /opt/matrix

# 创建完整的目录结构
mkdir -p {config,data,logs,scripts,backup}
mkdir -p data/{postgres,redis,synapse,nginx,coturn}
mkdir -p data/{nginx/{conf,certs,logs},coturn/{conf,certs,logs}}
mkdir -p data/synapse/{media,logs}

# 验证目录结构
tree /opt/matrix
```

**预期输出**：
```
/opt/matrix
├── backup
├── config
├── data
│   ├── coturn
│   │   ├── certs
│   │   ├── conf
│   │   └── logs
│   ├── nginx
│   │   ├── certs
│   │   ├── conf
│   │   └── logs
│   ├── postgres
│   ├── redis
│   └── synapse
│       ├── logs
│       └── media
├── logs
└── scripts
```

### 第二步：设置目录权限

> 🔐 **权限配置**：确保容器可以正确访问文件

```bash
# 设置目录所有者
sudo chown -R matrix:matrix /opt/matrix

# 设置适当的权限
chmod -R 755 /opt/matrix
chmod -R 750 /opt/matrix/data

# 验证权限
ls -la /opt/matrix
```

**预期输出**：
```
drwxr-xr-x  6 <USER> <GROUP> 4096 Dec 14 10:00 backup
drwxr-xr-x  2 <USER> <GROUP> 4096 Dec 14 10:00 config
drwxr-x---  6 <USER> <GROUP> 4096 Dec 14 10:00 data
drwxr-xr-x  2 <USER> <GROUP> 4096 Dec 14 10:00 logs
drwxr-xr-x  2 <USER> <GROUP> 4096 Dec 14 10:00 scripts
```

---

## ⚙️ 配置文件设置

### 第一步：创建环境配置文件

> 📝 **配置说明**：这个文件包含所有服务的配置参数

```bash
# 进入配置目录
cd /opt/matrix/config

# 创建环境配置文件
cat > deployment.env << 'EOF'
# Matrix Homeserver Podman版本配置文件
#
# 重要提醒：
# 1. 请将所有 "your_xxx_here" 替换为实际值
# 2. 所有密码都必须设置为强密码
# 3. 域名必须是您实际拥有的域名

# ================================================================
# 基础域名配置（必须修改）
# ================================================================

# 主域名 - 请替换为您的实际域名
DOMAIN="example.com"

# Matrix服务子域名 - 完整地址将是 matrix.example.com
SUBDOMAIN_MATRIX="matrix"

# HTTPS服务端口 - Matrix官方标准端口
HTTPS_PORT="8448"

# ================================================================
# 数据库配置
# ================================================================

# PostgreSQL数据库配置
DB_NAME="synapse"
DB_USER="synapse"

# 数据库密码 - 请设置强密码
# 生成强密码命令：openssl rand -base64 32
DB_PASSWORD="your_secure_database_password_here"

# ================================================================
# Cloudflare DNS配置（必须配置）
# ================================================================

# Cloudflare API令牌 - 从Cloudflare控制台获取
CLOUDFLARE_API_TOKEN="your_cloudflare_api_token_here"

# Cloudflare Zone ID - 从域名管理页面获取
CLOUDFLARE_ZONE_ID="your_cloudflare_zone_id_here"

# Cloudflare账户邮箱
CLOUDFLARE_EMAIL="<EMAIL>"

# ================================================================
# TURN服务器配置（音视频通话）
# ================================================================

# Coturn共享密钥 - 请设置强密钥
# 生成命令：openssl rand -base64 32
COTURN_SHARED_SECRET="your_coturn_shared_secret_here"

# TURN服务端口
TURN_PORT="3478"
TURNS_PORT="5349"
COTURN_MIN_PORT="49152"
COTURN_MAX_PORT="65535"

# ================================================================
# Synapse配置
# ================================================================

# 管理员用户名
SYNAPSE_ADMIN_USER="admin"

# Synapse密钥 - 请设置强密钥
# 生成命令：openssl rand -base64 32
SYNAPSE_FORM_SECRET="your_synapse_form_secret_here"
SYNAPSE_MACAROON_SECRET="your_synapse_macaroon_secret_here"

# 用户注册设置
ENABLE_REGISTRATION="false"  # 是否允许新用户注册

# 媒体文件配置
MAX_UPLOAD_SIZE="50M"        # 最大上传文件大小

# ================================================================
# 容器资源限制
# ================================================================

# PostgreSQL容器资源
POSTGRES_MEMORY_LIMIT="1g"
POSTGRES_CPU_LIMIT="1"

# Redis容器资源
REDIS_MEMORY_LIMIT="512m"
REDIS_CPU_LIMIT="0.5"

# Synapse容器资源
SYNAPSE_MEMORY_LIMIT="2g"
SYNAPSE_CPU_LIMIT="2"

# Nginx容器资源
NGINX_MEMORY_LIMIT="256m"
NGINX_CPU_LIMIT="0.5"

# Coturn容器资源
COTURN_MEMORY_LIMIT="512m"
COTURN_CPU_LIMIT="1"
EOF
```

**⚠️ 重要提醒**：
1. 必须将所有 `your_xxx_here` 替换为实际值
2. 所有密码必须设置为强密码
3. 域名必须是您实际拥有的域名

### 第二步：生成强密码

> 🔐 **安全配置**：生成安全的密码和密钥

```bash
# 生成数据库密码
echo "数据库密码："
openssl rand -base64 32

# 生成Coturn密钥
echo "Coturn密钥："
openssl rand -base64 32

# 生成Synapse表单密钥
echo "Synapse表单密钥："
openssl rand -base64 32

# 生成Synapse Macaroon密钥
echo "Synapse Macaroon密钥："
openssl rand -base64 32
```

**预期输出**：
```
数据库密码：
K8mN2pQ7vX9wR5tY3uI6oE1sA4dF2gH9

Coturn密钥：
L9nO3qR8wY0xS6uZ4vJ7pF2tB5eG3hI0

Synapse表单密钥：
M0oP4rS9xZ1yT7vA5wK8qG3uC6fH4jL1

Synapse Macaroon密钥：
N1pQ5sT0yA2zU8wB6xL9rH4vD7gI5kM2
```

> 📝 **操作说明**：请将这些生成的密码复制并替换到 `deployment.env` 文件中对应的位置

### 第三步：编辑配置文件

> ✏️ **配置编辑**：使用文本编辑器修改配置文件

```bash
# 使用nano编辑器打开配置文件
nano deployment.env
```

**需要修改的配置项**：
1. `DOMAIN` - 替换为您的域名
2. `CLOUDFLARE_API_TOKEN` - 从Cloudflare获取
3. `CLOUDFLARE_ZONE_ID` - 从Cloudflare获取
4. `CLOUDFLARE_EMAIL` - 您的邮箱
5. 所有密码字段 - 使用上面生成的强密码

**编辑完成后**：
- 按 `Ctrl + X` 退出
- 按 `Y` 确认保存
- 按 `Enter` 确认文件名

### 第四步：创建Podman Compose文件

> 🐳 **容器编排**：定义所有服务的容器配置

```bash
# 创建Podman Compose配置文件
cat > docker-compose.yml << 'EOF'
# Matrix Homeserver Podman Compose配置文件
# 适配Podman的容器编排配置

version: '3.8'

# ================================================================
# 网络定义
# ================================================================
networks:
  matrix_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ================================================================
# 数据卷定义
# ================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  synapse_data:
    driver: local

# ================================================================
# 服务定义
# ================================================================
services:

  # PostgreSQL数据库服务
  # 说明：存储Matrix的所有数据，包括用户信息、房间数据、消息等
  db:
    image: postgres:15-alpine
    container_name: matrix_postgres
    restart: unless-stopped

    # 环境变量配置
    environment:
      POSTGRES_DB: ${DB_NAME:-synapse}
      POSTGRES_USER: ${DB_USER:-synapse}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"

    # 数据卷挂载
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./config/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql:ro

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${POSTGRES_MEMORY_LIMIT:-1g}
          cpus: '${POSTGRES_CPU_LIMIT:-1}'

    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-synapse} || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis缓存服务
  # 说明：提供缓存功能，提高Matrix服务器性能
  redis:
    image: redis:7-alpine
    container_name: matrix_redis
    restart: unless-stopped

    # Redis配置命令
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

    # 数据卷挂载
    volumes:
      - redis_data:/data

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT:-512m}
          cpus: '${REDIS_CPU_LIMIT:-0.5}'

    # 健康检查
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Synapse Matrix服务器
  # 说明：Matrix协议的核心实现，处理所有Matrix通信
  synapse:
    image: matrixdotorg/synapse:latest
    container_name: matrix_synapse
    restart: unless-stopped

    # 环境变量配置
    environment:
      SYNAPSE_SERVER_NAME: ${DOMAIN}
      SYNAPSE_REPORT_STATS: "no"
      SYNAPSE_CONFIG_PATH: /data/homeserver.yaml

    # 数据卷挂载
    volumes:
      - synapse_data:/data
      - ./config/homeserver.yaml:/data/homeserver.yaml:ro
      - ./config/log.config:/data/log.config:ro
      - ./data/synapse/media:/data/media_store
      - ./data/synapse/logs:/data/logs

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${SYNAPSE_MEMORY_LIMIT:-2g}
          cpus: '${SYNAPSE_CPU_LIMIT:-2}'

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/_matrix/client/versions || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx反向代理服务
  # 说明：处理HTTPS请求，提供SSL终端，转发请求到Synapse
  nginx:
    image: nginx:alpine
    container_name: matrix_nginx
    restart: unless-stopped

    # 端口映射 - 绑定到本地接口
    ports:
      - "127.0.0.1:${HTTPS_PORT:-8448}:${HTTPS_PORT:-8448}"

    # 数据卷挂载
    volumes:
      - ./data/nginx/conf/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./data/nginx/conf/matrix.conf:/etc/nginx/conf.d/matrix.conf:ro
      - ./data/nginx/certs:/etc/nginx/certs:ro
      - ./data/nginx/logs:/var/log/nginx
      - ./data/synapse/media:/var/www/matrix/media:ro

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${NGINX_MEMORY_LIMIT:-256m}
          cpus: '${NGINX_CPU_LIMIT:-0.5}'

    # 健康检查
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Coturn TURN/STUN服务器
  # 说明：用于音视频通话的NAT穿透服务
  coturn:
    image: coturn/coturn:latest
    container_name: matrix_coturn
    restart: unless-stopped

    # 端口映射 - Podman无根容器使用端口映射替代host网络
    ports:
      - "3478:3478/tcp"
      - "3478:3478/udp"
      - "5349:5349/tcp"
      - "49152-65535:49152-65535/udp"

    # 数据卷挂载
    volumes:
      - ./data/coturn/conf/turnserver.conf:/etc/coturn/turnserver.conf:ro
      - ./data/coturn/certs:/etc/coturn/certs:ro
      - ./data/coturn/logs:/var/log/coturn

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${COTURN_MEMORY_LIMIT:-512m}
          cpus: '${COTURN_CPU_LIMIT:-1}'
EOF
```

### 第五步：创建数据库初始化脚本

> 🗄️ **数据库配置**：创建PostgreSQL初始化脚本

```bash
# 创建PostgreSQL初始化脚本
cat > postgres-init.sql << 'EOF'
-- PostgreSQL初始化脚本
-- 为Matrix Synapse创建数据库和用户

-- 创建数据库（如果不存在）
CREATE DATABASE synapse
    ENCODING 'UTF8'
    LC_COLLATE 'C'
    LC_CTYPE 'C'
    TEMPLATE template0;

-- 创建用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'synapse') THEN
        CREATE USER synapse WITH PASSWORD 'placeholder_password';
    END IF;
END
$$;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE synapse TO synapse;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO synapse;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO synapse;
EOF
```

### 第六步：创建Synapse配置文件

> ⚙️ **Matrix核心配置**：配置Synapse服务器

```bash
# 创建Synapse主配置文件
cat > homeserver.yaml << 'EOF'
# Matrix Synapse Homeserver配置文件
# 详细配置说明：https://matrix-org.github.io/synapse/latest/usage/configuration/

# ================================================================
# 服务器基础配置
# ================================================================

# 服务器名称（必须与域名匹配）
server_name: "example.com"

# PID文件位置
pid_file: /data/homeserver.pid

# 公共访问URL（客户端连接地址）
public_baseurl: https://matrix.example.com:8448/

# Web客户端位置（重定向到Element）
web_client_location: https://app.element.io/

# ================================================================
# 网络监听配置
# ================================================================

listeners:
  # HTTP监听器（内部通信）
  - port: 8008
    tls: false
    type: http
    x_forwarded: true  # 信任反向代理的X-Forwarded-* 头部
    bind_addresses: ['0.0.0.0']

    resources:
      # 客户端API和联邦API
      - names: [client, federation]
        compress: false  # 由Nginx处理压缩

# ================================================================
# 数据库配置
# ================================================================

database:
  name: psycopg2  # PostgreSQL驱动
  args:
    user: synapse
    password: placeholder_password  # 将被脚本替换
    database: synapse
    host: db  # Docker容器名称
    port: 5432
    cp_min: 5      # 最小连接池大小
    cp_max: 10     # 最大连接池大小

# ================================================================
# Redis缓存配置
# ================================================================

redis:
  enabled: true
  host: redis    # Docker容器名称
  port: 6379

# ================================================================
# 媒体存储配置
# ================================================================

media_store_path: /data/media_store

# 媒体文件大小限制
max_upload_size: 50M

# 媒体文件保留策略
media_retention:
  # 本地媒体保留时间（天）
  local_media_lifetime: 365d
  # 远程媒体保留时间（天）
  remote_media_lifetime: 90d

# ================================================================
# 用户注册配置
# ================================================================

# 是否允许新用户注册
enable_registration: false

# 注册共享密钥（如果启用注册）
# registration_shared_secret: "your_registration_secret"

# ================================================================
# 联邦配置
# ================================================================

# 联邦域名白名单（空列表表示允许所有域名）
federation_domain_whitelist: []

# 联邦IP黑名单（阻止私有网络）
federation_ip_range_blacklist:
  - '*********/8'    # 本地回环
  - '10.0.0.0/8'     # 私有网络A类
  - '**********/12'  # 私有网络B类
  - '***********/16' # 私有网络C类
  - '**********/10'  # 运营商级NAT
  - '***********/16' # 链路本地
  - '::1/128'        # IPv6本地回环
  - 'fe80::/64'      # IPv6链路本地
  - 'fc00::/7'       # IPv6私有网络

# ================================================================
# TURN服务器配置（用于音视频通话）
# ================================================================

turn_uris:
  - "turn:matrix.example.com:3478?transport=udp"
  - "turn:matrix.example.com:3478?transport=tcp"
  - "turns:matrix.example.com:5349?transport=tcp"

turn_shared_secret: "placeholder_coturn_secret"  # 将被脚本替换
turn_user_lifetime: 1h      # TURN用户生存时间
turn_allow_guests: true     # 允许访客使用TURN

# ================================================================
# 安全配置
# ================================================================

# 密码哈希轮数
bcrypt_rounds: 12

# 表单密钥（用于CSRF保护）
form_secret: "placeholder_form_secret"  # 将被脚本替换

# Macaroon密钥（用于访问令牌）
macaroon_secret_key: "placeholder_macaroon_secret"  # 将被脚本替换

# ================================================================
# 速率限制配置
# ================================================================

# 消息发送速率限制
rc_message:
  per_second: 0.2    # 每秒最多0.2条消息
  burst_count: 10    # 突发最多10条消息

# 用户注册速率限制
rc_registration:
  per_second: 0.17   # 每秒最多0.17次注册
  burst_count: 3     # 突发最多3次注册

# 登录速率限制
rc_login:
  address:
    per_second: 0.17
    burst_count: 3
  account:
    per_second: 0.17
    burst_count: 3
  failed_attempts:
    per_second: 0.17
    burst_count: 3

# ================================================================
# 日志配置
# ================================================================

log_config: "/data/log.config"

# ================================================================
# 签名密钥配置
# ================================================================

signing_key_path: "/data/signing.key"

# 信任的密钥服务器
trusted_key_servers:
  - server_name: "matrix.org"

# 抑制密钥服务器警告
suppress_key_server_warning: true

# ================================================================
# 实验性功能
# ================================================================

experimental_features:
  spaces_enabled: true      # 启用Spaces功能
  msc3026_enabled: true     # 启用忙碌状态
EOF
```

### 第七步：创建日志配置文件

> 📝 **日志配置**：配置Synapse的日志输出

```bash
# 创建Synapse日志配置
cat > log.config << 'EOF'
# Synapse日志配置文件

version: 1

formatters:
  precise:
    format: '%(asctime)s - %(name)s - %(lineno)d - %(levelname)s - %(request)s - %(message)s'

handlers:
  file:
    class: logging.handlers.TimedRotatingFileHandler
    formatter: precise
    filename: /data/logs/homeserver.log
    when: midnight
    backupCount: 7  # 保留7天的日志
    encoding: utf8

  console:
    class: logging.StreamHandler
    formatter: precise

loggers:
  synapse.storage.SQL:
    # 数据库查询日志级别
    level: INFO

  synapse.http.server:
    # HTTP服务器日志级别
    level: INFO

root:
  level: INFO
  handlers: [file, console]

disable_existing_loggers: false
EOF
```

### 第八步：创建Nginx配置文件

> 🌐 **反向代理配置**：配置Nginx处理HTTPS请求

```bash
# 创建Nginx主配置文件
mkdir -p /opt/matrix/data/nginx/conf

cat > /opt/matrix/data/nginx/conf/nginx.conf << 'EOF'
# Nginx主配置文件
# 针对Matrix Homeserver优化

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 工作进程配置
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# HTTP配置块
http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # 客户端配置
    client_max_body_size 50M;  # Matrix媒体文件上传限制
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
EOF

# 创建Matrix站点配置
cat > /opt/matrix/data/nginx/conf/matrix.conf << 'EOF'
# Matrix Homeserver站点配置
# 处理Matrix客户端和联邦服务器的请求

server {
    listen 8448 ssl http2;
    server_name matrix.example.com;  # 请替换为您的实际域名

    # SSL证书配置（使用符号链接）
    ssl_certificate /etc/nginx/certs/fullchain.cer;
    ssl_certificate_key /etc/nginx/certs/private.key;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头部
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Matrix客户端API代理
    location /_matrix {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;

        # 客户端IP传递
        proxy_set_header X-Real-IP $remote_addr;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 缓冲配置
        proxy_buffering off;
    }

    # 健康检查端点
    location /_matrix/client/versions {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;

        # 缓存配置
        add_header Cache-Control "public, max-age=3600";
    }

    # 媒体文件代理（大文件上传）
    location /_matrix/media {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;

        # 大文件上传配置
        client_max_body_size 50M;
        proxy_request_buffering off;
    }

    # 根路径重定向到Element Web
    location = / {
        return 301 https://app.element.io/;
    }

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
EOF
```

### 第九步：创建Coturn配置文件

> 📞 **音视频服务配置**：配置TURN/STUN服务器

```bash
# 创建Coturn配置目录
mkdir -p /opt/matrix/data/coturn/conf

# 创建Coturn配置文件
cat > /opt/matrix/data/coturn/conf/turnserver.conf << 'EOF'
# Coturn TURN/STUN服务器配置文件
# 用于Matrix音视频通话的NAT穿透

# ================================================================
# 基础服务配置
# ================================================================

# 监听端口配置
listening-port=3478          # STUN/TURN端口
tls-listening-port=5349      # TURNS端口（TLS加密）

# 监听地址（0.0.0.0表示所有接口）
listening-ip=0.0.0.0

# 外部IP地址（将被脚本自动更新）
external-ip=YOUR_EXTERNAL_IP

# ================================================================
# 中继端口范围配置
# ================================================================

# UDP中继端口范围（需要在路由器上开放这些端口）
min-port=49152
max-port=65535

# ================================================================
# 认证配置
# ================================================================

# 使用长期凭证机制
use-auth-secret
static-auth-secret=placeholder_coturn_secret  # 将被脚本替换

# 认证域名
realm=matrix.example.com  # 请替换为您的实际域名

# ================================================================
# SSL/TLS证书配置
# ================================================================

# 证书文件路径（使用符号链接）
cert=/etc/coturn/certs/fullchain.cer
pkey=/etc/coturn/certs/private.key

# ================================================================
# 安全配置
# ================================================================

# 禁用不安全的协议
no-sslv2
no-sslv3

# 启用指纹验证
fingerprint

# 禁用CLI（命令行接口）
no-cli

# ================================================================
# 日志配置
# ================================================================

# 日志文件路径
log-file=/var/log/coturn/turnserver.log

# 详细日志级别
verbose

# ================================================================
# 性能优化配置
# ================================================================

# 用户配额（每个用户最大会话数）
user-quota=12

# 总配额（服务器最大会话数）
total-quota=1200

# ================================================================
# 网络配置
# ================================================================

# 禁用本地回环地址
no-loopback-peers

# 禁用多播地址
no-multicast-peers

# 允许的对等地址范围
denied-peer-ip=0.0.0.0-*************
denied-peer-ip=10.0.0.0-1*************
denied-peer-ip=**********-***************
denied-peer-ip=*********-***************
denied-peer-ip=***********-***************
denied-peer-ip=**********-**************
denied-peer-ip=*********-***********
denied-peer-ip=*********-***********
denied-peer-ip=***********-*************
denied-peer-ip=***********-***************
denied-peer-ip=**********-**************
denied-peer-ip=************-**************
denied-peer-ip=***********-*************
denied-peer-ip=240.0.0.0-***************

# IPv6禁用范围
denied-peer-ip=::1
denied-peer-ip=64:ff9b::-64:ff9b::ffff:ffff
denied-peer-ip=::ffff:0.0.0.0-::ffff:***************
denied-peer-ip=2001::-2001:1ff:ffff:ffff:ffff:ffff:ffff:ffff
denied-peer-ip=2002::-2002:ffff:ffff:ffff:ffff:ffff:ffff:ffff
denied-peer-ip=fc00::-fdff:ffff:ffff:ffff:ffff:ffff:ffff:ffff
denied-peer-ip=fe80::-febf:ffff:ffff:ffff:ffff:ffff:ffff:ffff
EOF
```

---

## 🚀 服务部署

### 第一步：创建部署脚本

> 🔧 **自动化部署**：创建一键部署脚本

```bash
# 进入项目目录
cd /opt/matrix

# 创建部署脚本
cat > scripts/deploy.sh << 'EOF'
#!/bin/bash
# Matrix Homeserver Podman部署脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }

# 加载配置文件
if [[ ! -f "config/deployment.env" ]]; then
    log_error "配置文件不存在: config/deployment.env"
    exit 1
fi

source config/deployment.env

# 验证必需配置
required_vars=("DOMAIN" "DB_PASSWORD" "COTURN_SHARED_SECRET" "SYNAPSE_FORM_SECRET" "SYNAPSE_MACAROON_SECRET")
for var in "${required_vars[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        log_error "缺少必需配置: $var"
        exit 1
    fi
done

log_info "开始部署Matrix Homeserver (Podman版本)"
log_info "域名: $DOMAIN"
log_info "Matrix服务: ${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT}"

# 替换配置文件中的占位符
log_info "更新配置文件..."

# 更新Synapse配置
sed -i "s/example.com/${DOMAIN}/g" config/homeserver.yaml
sed -i "s/matrix.example.com/${SUBDOMAIN_MATRIX}.${DOMAIN}/g" config/homeserver.yaml
sed -i "s/placeholder_password/${DB_PASSWORD}/g" config/homeserver.yaml
sed -i "s/placeholder_coturn_secret/${COTURN_SHARED_SECRET}/g" config/homeserver.yaml
sed -i "s/placeholder_form_secret/${SYNAPSE_FORM_SECRET}/g" config/homeserver.yaml
sed -i "s/placeholder_macaroon_secret/${SYNAPSE_MACAROON_SECRET}/g" config/homeserver.yaml

# 更新Nginx配置
sed -i "s/matrix.example.com/${SUBDOMAIN_MATRIX}.${DOMAIN}/g" data/nginx/conf/matrix.conf

# 更新Coturn配置
sed -i "s/matrix.example.com/${SUBDOMAIN_MATRIX}.${DOMAIN}/g" data/coturn/conf/turnserver.conf
sed -i "s/placeholder_coturn_secret/${COTURN_SHARED_SECRET}/g" data/coturn/conf/turnserver.conf

# 更新数据库初始化脚本
sed -i "s/placeholder_password/${DB_PASSWORD}/g" config/postgres-init.sql

log_info "配置文件更新完成"

# 拉取镜像
log_info "拉取容器镜像..."
if podman-compose pull; then
    log_info "镜像拉取完成"
else
    log_warn "镜像拉取失败，将使用本地镜像"
fi

# 启动服务
log_info "启动服务..."

# 第一阶段：启动数据库和Redis
log_info "启动数据库和Redis服务..."
podman-compose up -d db redis

# 等待数据库启动
log_info "等待数据库启动..."
for i in {1..30}; do
    if podman exec matrix_postgres pg_isready -U synapse >/dev/null 2>&1; then
        log_info "数据库启动完成"
        break
    fi
    sleep 2
    echo -n "."
done
echo

# 第二阶段：启动Synapse
log_info "启动Synapse服务..."
podman-compose up -d synapse

# 等待Synapse启动
log_info "等待Synapse启动..."
for i in {1..60}; do
    if curl -s http://localhost:8008/_matrix/client/versions >/dev/null 2>&1; then
        log_info "Synapse启动完成"
        break
    fi
    sleep 2
    echo -n "."
done
echo

# 第三阶段：启动Nginx和Coturn
log_info "启动Nginx和Coturn服务..."
podman-compose up -d nginx coturn

log_info "所有服务启动完成"

# 显示服务状态
echo
log_info "服务状态:"
podman-compose ps

echo
log_info "部署完成！"
log_info "下一步："
log_info "1. 配置SSL证书"
log_info "2. 创建管理员用户"
log_info "3. 测试服务"
EOF

# 设置执行权限
chmod +x scripts/deploy.sh
```

### 第二步：运行部署脚本

> 🎯 **执行部署**：运行自动化部署脚本

```bash
# 确保在正确的目录
cd /opt/matrix

# 运行部署脚本
./scripts/deploy.sh
```

**预期输出**：
```
[INFO] 开始部署Matrix Homeserver (Podman版本)
[INFO] 域名: example.com
[INFO] Matrix服务: matrix.example.com:8448
[INFO] 更新配置文件...
[INFO] 配置文件更新完成
[INFO] 拉取容器镜像...
[INFO] 镜像拉取完成
[INFO] 启动服务...
[INFO] 启动数据库和Redis服务...
[INFO] 等待数据库启动...
[INFO] 数据库启动完成
[INFO] 启动Synapse服务...
[INFO] 等待Synapse启动...
[INFO] Synapse启动完成
[INFO] 启动Nginx和Coturn服务...
[INFO] 所有服务启动完成

[INFO] 服务状态:
CONTAINER ID  IMAGE                           COMMAND               CREATED        STATUS            PORTS                 NAMES
abc123def456  docker.io/library/postgres:15-alpine  postgres          2 minutes ago  Up 2 minutes ago                        matrix_postgres
def456ghi789  docker.io/library/redis:7-alpine      redis-server      2 minutes ago  Up 2 minutes ago                        matrix_redis
ghi789jkl012  docker.io/matrixdotorg/synapse:latest  python -m syn...  1 minute ago   Up 1 minute ago                         matrix_synapse
jkl012mno345  docker.io/library/nginx:alpine        nginx -g daemon   30 seconds ago Up 30 seconds ago 127.0.0.1:8448->8448/tcp matrix_nginx
mno345pqr678  docker.io/coturn/coturn:latest         turnserver        30 seconds ago Up 30 seconds ago 3478/tcp,5349/tcp     matrix_coturn

[INFO] 部署完成！
```

### 第三步：检查服务状态

> ✅ **状态验证**：确认所有服务正常运行

```bash
# 检查容器状态
podman-compose ps

# 检查容器日志
podman-compose logs db
podman-compose logs redis
podman-compose logs synapse
podman-compose logs nginx
podman-compose logs coturn

# 检查端口监听
sudo netstat -tlnp | grep -E "(5432|6379|8008|8448|3478|5349)"
```

**预期输出**：
```
tcp        0      0 127.0.0.1:8448          0.0.0.0:*               LISTEN      -
tcp        0      0 0.0.0.0:3478            0.0.0.0:*               LISTEN      -
tcp        0      0 0.0.0.0:5349            0.0.0.0:*               LISTEN      -
udp        0      0 0.0.0.0:3478            0.0.0.0:*                           -
```

---

## ✅ 验证测试

### 第一步：基础连通性测试

> 🔍 **网络测试**：验证服务基础功能

```bash
# 测试PostgreSQL连接
podman exec matrix_postgres pg_isready -U synapse
# 预期输出：/var/run/postgresql:5432 - accepting connections

# 测试Redis连接
podman exec matrix_redis redis-cli ping
# 预期输出：PONG

# 测试Synapse API
curl -s http://localhost:8008/_matrix/client/versions | jq .
# 预期输出：JSON格式的版本信息

# 测试Nginx配置
podman exec matrix_nginx nginx -t
# 预期输出：nginx: configuration file /etc/nginx/nginx.conf test is successful
```

### 第二步：创建管理员用户

> 👤 **用户管理**：创建Matrix管理员账户

```bash
# 进入Synapse容器创建管理员用户
podman exec -it matrix_synapse register_new_matrix_user \
    -c /data/homeserver.yaml \
    http://localhost:8008

# 按提示操作：
# New user localpart [root]: admin
# Password: [输入强密码]
# Confirm password: [再次输入密码]
# Make admin [no]: yes
```

**预期输出**：
```
New user localpart [root]: admin
Password:
Confirm password:
Make admin [no]: yes
Sending registration request...
Success!
```

### 第三步：SSL证书配置

> 🔒 **安全配置**：配置HTTPS证书

```bash
# 安装acme.sh
curl https://get.acme.sh | sh -s email=<EMAIL>

# 重新加载shell配置
source ~/.bashrc

# 设置Cloudflare API环境变量
export CF_Token="your_cloudflare_api_token"
export CF_Zone_ID="your_zone_id"

# 申请SSL证书
acme.sh --issue --dns dns_cf -d matrix.example.com --keylength ec-256

# 创建证书符号链接
DOMAIN="matrix.example.com"
CERT_SOURCE="/root/.acme.sh/${DOMAIN}_ecc"

# 创建证书目录
mkdir -p /opt/matrix/data/nginx/certs
mkdir -p /opt/matrix/data/coturn/certs

# 创建符号链接
ln -sf "${CERT_SOURCE}/fullchain.cer" "/opt/matrix/data/nginx/certs/fullchain.cer"
ln -sf "${CERT_SOURCE}/${DOMAIN}.key" "/opt/matrix/data/nginx/certs/private.key"
ln -sf "${CERT_SOURCE}/fullchain.cer" "/opt/matrix/data/coturn/certs/fullchain.cer"
ln -sf "${CERT_SOURCE}/${DOMAIN}.key" "/opt/matrix/data/coturn/certs/private.key"

# 重启Nginx和Coturn服务
podman-compose restart nginx coturn
```

### 第四步：完整功能测试

> 🧪 **功能验证**：测试Matrix服务完整功能

```bash
# 测试Matrix API（HTTPS）
curl -k https://matrix.example.com:8448/_matrix/client/versions

# 测试联邦API
curl -k https://matrix.example.com:8448/_matrix/federation/v1/version

# 测试TURN服务器
# 使用在线STUN/TURN测试工具：https://webrtc.github.io/samples/src/content/peerconnection/trickle-ice/
```

**预期输出**：
```json
{
  "versions": [
    "r0.0.1",
    "r0.1.0",
    "r0.2.0",
    "r0.3.0",
    "r0.4.0",
    "r0.5.0",
    "r0.6.0",
    "r0.6.1",
    "v1.1",
    "v1.2",
    "v1.3",
    "v1.4",
    "v1.5"
  ],
  "unstable_features": {
    "org.matrix.label_based_filtering": true,
    "org.matrix.e2e_cross_signing": true,
    "org.matrix.msc2432": true,
    "uk.half-shot.msc2666.mutual_rooms": true,
    "io.element.e2ee_forced.public": false,
    "io.element.e2ee_forced.private": false,
    "io.element.e2ee_forced.trusted_private": false,
    "org.matrix.msc3026.busy_presence": false,
    "org.matrix.msc2716": false,
    "org.matrix.msc3440.stable": true,
    "org.matrix.msc3771": true,
    "org.matrix.msc3773": false,
    "fi.mau.msc2815": false,
    "org.matrix.msc3882": false,
    "org.matrix.msc3881": false,
    "org.matrix.msc3874": false
  }
}
```

---

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 容器启动失败

**问题症状**：
```bash
podman-compose ps
# 显示容器状态为 Exited 或 Error
```

**解决步骤**：
```bash
# 查看容器日志
podman-compose logs [服务名]

# 常见原因和解决方案：

# 权限问题
sudo chown -R matrix:matrix /opt/matrix/data
chmod -R 755 /opt/matrix/data

# 端口冲突
sudo netstat -tlnp | grep [端口号]
# 如果端口被占用，修改配置文件中的端口

# 配置文件错误
nano config/deployment.env
# 检查所有必需配置项是否正确设置
```

#### 2. 数据库连接失败

**问题症状**：
```
synapse    | psycopg2.OperationalError: could not connect to server
```

**解决步骤**：
```bash
# 检查PostgreSQL容器状态
podman exec matrix_postgres pg_isready -U synapse

# 如果失败，重启数据库
podman-compose restart db

# 等待数据库启动
sleep 30

# 重启Synapse
podman-compose restart synapse
```

#### 3. SSL证书问题

**问题症状**：
```
nginx    | SSL: error:02001002:system library:fopen:No such file or directory
```

**解决步骤**：
```bash
# 检查证书文件是否存在
ls -la /opt/matrix/data/nginx/certs/

# 如果证书不存在，重新申请
acme.sh --issue --dns dns_cf -d matrix.example.com --keylength ec-256

# 重新创建符号链接
DOMAIN="matrix.example.com"
CERT_SOURCE="/root/.acme.sh/${DOMAIN}_ecc"
ln -sf "${CERT_SOURCE}/fullchain.cer" "/opt/matrix/data/nginx/certs/fullchain.cer"
ln -sf "${CERT_SOURCE}/${DOMAIN}.key" "/opt/matrix/data/nginx/certs/private.key"

# 重启Nginx
podman-compose restart nginx
```

#### 4. 网络连接问题

**问题症状**：
```
curl: (7) Failed to connect to matrix.example.com port 8448
```

**解决步骤**：
```bash
# 检查防火墙设置
sudo ufw status

# 检查端口转发（如果在内网）
# 确保路由器已配置端口转发：8448 -> 内网IP:8448

# 检查DNS解析
nslookup matrix.example.com

# 检查服务监听状态
sudo netstat -tlnp | grep 8448
```

#### 5. 内存不足问题

**问题症状**：
```
synapse    | MemoryError: Unable to allocate memory
```

**解决步骤**：
```bash
# 检查系统内存使用
free -h

# 调整容器资源限制
nano config/deployment.env
# 减少 SYNAPSE_MEMORY_LIMIT 等配置

# 重启服务
podman-compose down
podman-compose up -d
```

### 调试命令集合

```bash
# 查看所有容器状态
podman-compose ps

# 查看特定服务日志
podman-compose logs -f synapse
podman-compose logs -f db
podman-compose logs -f nginx

# 进入容器调试
podman exec -it matrix_synapse /bin/bash
podman exec -it matrix_postgres /bin/bash

# 检查容器资源使用
podman stats

# 检查网络连接
podman exec matrix_synapse netstat -tlnp

# 重启特定服务
podman-compose restart synapse

# 完全重启所有服务
podman-compose down
podman-compose up -d
```

---

## 🔄 日常维护

### 定期维护任务

#### 1. 日志管理

```bash
# 创建日志清理脚本
cat > scripts/cleanup_logs.sh << 'EOF'
#!/bin/bash
# 日志清理脚本

LOG_DIR="/opt/matrix/data"
DAYS_TO_KEEP=30

# 清理Nginx日志
find ${LOG_DIR}/nginx/logs -name "*.log" -mtime +${DAYS_TO_KEEP} -delete

# 清理Synapse日志
find ${LOG_DIR}/synapse/logs -name "*.log" -mtime +${DAYS_TO_KEEP} -delete

# 清理Coturn日志
find ${LOG_DIR}/coturn/logs -name "*.log" -mtime +${DAYS_TO_KEEP} -delete

# 清理容器日志
podman system prune -f

echo "日志清理完成"
EOF

chmod +x scripts/cleanup_logs.sh

# 设置定时任务
crontab -e
# 添加：0 2 * * 0 /opt/matrix/scripts/cleanup_logs.sh
```

#### 2. 数据库维护

```bash
# 创建数据库维护脚本
cat > scripts/db_maintenance.sh << 'EOF'
#!/bin/bash
# 数据库维护脚本

# 数据库清理
podman exec matrix_postgres psql -U synapse -d synapse -c "VACUUM ANALYZE;"

# 检查数据库大小
podman exec matrix_postgres psql -U synapse -d synapse -c "SELECT pg_size_pretty(pg_database_size('synapse'));"

echo "数据库维护完成"
EOF

chmod +x scripts/db_maintenance.sh
```

#### 3. 备份脚本

```bash
# 创建备份脚本
cat > scripts/backup.sh << 'EOF'
#!/bin/bash
# 备份脚本

BACKUP_DIR="/opt/matrix/backup"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p ${BACKUP_DIR}

# 备份数据库
podman exec matrix_postgres pg_dump -U synapse synapse > ${BACKUP_DIR}/synapse_${DATE}.sql

# 备份配置文件
tar -czf ${BACKUP_DIR}/config_${DATE}.tar.gz -C /opt/matrix config

# 备份媒体文件（可选，文件较大）
# tar -czf ${BACKUP_DIR}/media_${DATE}.tar.gz -C /opt/matrix/data/synapse media

# 清理旧备份（保留30天）
find ${BACKUP_DIR} -name "*.sql" -mtime +30 -delete
find ${BACKUP_DIR} -name "*.tar.gz" -mtime +30 -delete

echo "备份完成: ${DATE}"
EOF

chmod +x scripts/backup.sh
```

#### 4. 更新脚本

```bash
# 创建更新脚本
cat > scripts/update.sh << 'EOF'
#!/bin/bash
# 更新脚本

echo "开始更新Matrix Homeserver..."

# 备份当前配置
./scripts/backup.sh

# 拉取最新镜像
podman-compose pull

# 重启服务
podman-compose down
podman-compose up -d

# 等待服务启动
sleep 60

# 检查服务状态
podman-compose ps

echo "更新完成"
EOF

chmod +x scripts/update.sh
```

### 监控脚本

```bash
# 创建健康检查脚本
cat > scripts/health_check.sh << 'EOF'
#!/bin/bash
# 健康检查脚本

# 检查容器状态
echo "=== 容器状态 ==="
podman-compose ps

# 检查API响应
echo "=== API检查 ==="
if curl -s http://localhost:8008/_matrix/client/versions >/dev/null; then
    echo "✅ Synapse API正常"
else
    echo "❌ Synapse API异常"
fi

# 检查数据库
echo "=== 数据库检查 ==="
if podman exec matrix_postgres pg_isready -U synapse >/dev/null 2>&1; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接异常"
fi

# 检查Redis
echo "=== Redis检查 ==="
if podman exec matrix_redis redis-cli ping >/dev/null 2>&1; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接异常"
fi

# 检查磁盘空间
echo "=== 磁盘空间 ==="
df -h /opt/matrix

# 检查内存使用
echo "=== 内存使用 ==="
free -h
EOF

chmod +x scripts/health_check.sh
```

### 定时任务配置

```bash
# 编辑crontab
crontab -e

# 添加以下定时任务：
# 每天凌晨2点备份
0 2 * * * /opt/matrix/scripts/backup.sh >> /var/log/matrix/backup.log 2>&1

# 每周日凌晨3点清理日志
0 3 * * 0 /opt/matrix/scripts/cleanup_logs.sh >> /var/log/matrix/cleanup.log 2>&1

# 每月1号凌晨4点数据库维护
0 4 1 * * /opt/matrix/scripts/db_maintenance.sh >> /var/log/matrix/maintenance.log 2>&1

# 每5分钟健康检查
*/5 * * * * /opt/matrix/scripts/health_check.sh >> /var/log/matrix/health.log 2>&1
```

---

## 🎉 部署完成

恭喜！您已经成功部署了Matrix Homeserver (Podman版本)。

### 📱 客户端连接

您可以使用以下客户端连接到您的Matrix服务器：

1. **Element Web**: https://app.element.io/
2. **Element Desktop**: 下载桌面版客户端
3. **Element Mobile**: iOS/Android应用

**连接设置**：
- 服务器地址：`https://matrix.your-domain.com:8448`
- 用户名：`@admin:your-domain.com`
- 密码：您设置的管理员密码

### 🔗 有用的链接

- [Matrix官方文档](https://matrix.org/docs/)
- [Synapse管理指南](https://matrix-org.github.io/synapse/latest/)
- [Element客户端](https://element.io/)
- [Podman官方文档](https://docs.podman.io/)

### 📞 技术支持

如果遇到问题，可以：
1. 查看本指南的故障排除部分
2. 运行健康检查脚本：`./scripts/health_check.sh`
3. 查看服务日志：`podman-compose logs [服务名]`
4. 参考Matrix社区文档和论坛

祝您使用愉快！🎊
