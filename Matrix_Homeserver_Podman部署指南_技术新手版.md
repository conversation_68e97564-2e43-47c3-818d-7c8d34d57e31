# Matrix Homeserver Podman部署指南（技术新手版）

## 📋 目录

1. [<PERSON><PERSON> vs Docker对比](#podman-vs-docker对比)
2. [Podman适配方案](#podman适配方案)
3. [系统环境准备](#系统环境准备)
4. [Po<PERSON>安装和配置](#podman安装和配置)
5. [项目配置文件适配](#项目配置文件适配)
6. [脚本文件适配](#脚本文件适配)
7. [服务部署和配置](#服务部署和配置)
8. [故障排除指南](#故障排除指南)
9. [迁移步骤](#迁移步骤)

---

## 🔄 Podman vs Docker对比

### 什么是Podman？

**Podman**（Pod Manager）是一个无守护进程的容器引擎，用于在Linux系统上开发、管理和运行OCI容器。它是Docker的直接替代品，具有以下优势：

#### ✅ Podman的优势

1. **无守护进程架构**：
   - 不需要运行后台守护进程
   - 更安全，减少攻击面
   - 系统资源占用更少

2. **更好的安全性**：
   - 支持无根容器（rootless containers）
   - 更细粒度的权限控制
   - 符合SELinux安全策略

3. **兼容性**：
   - 命令行接口与Docker高度兼容
   - 支持Docker镜像和Dockerfile
   - 可以直接替换大部分Docker命令

4. **Pod支持**：
   - 原生支持Kubernetes Pod概念
   - 更好的容器组管理

#### ⚠️ 需要注意的差异

1. **Compose支持**：
   - 需要安装`podman-compose`或使用`podman compose`
   - 某些Docker Compose功能支持有限

2. **网络模式**：
   - 默认网络配置与Docker略有不同
   - `host`网络模式在无根模式下有限制

3. **存储驱动**：
   - 默认使用不同的存储驱动
   - 可能需要调整存储配置

---

## 🎯 Podman适配方案

### 兼容性矩阵

| 功能 | Docker | Podman | 适配状态 | 说明 |
|------|--------|--------|----------|------|
| 基本容器运行 | ✅ | ✅ | 完全兼容 | 无需修改 |
| 数据卷挂载 | ✅ | ✅ | 完全兼容 | 无需修改 |
| 网络配置 | ✅ | ✅ | 需要调整 | 语法略有不同 |
| Compose文件 | ✅ | ⚠️ | 需要适配 | 部分功能受限 |
| 健康检查 | ✅ | ⚠️ | 需要调整 | 语法不同 |
| 资源限制 | ✅ | ✅ | 需要调整 | 语法略有不同 |
| 服务依赖 | ✅ | ⚠️ | 需要重写 | 条件依赖不支持 |

### 主要适配内容

1. **Compose文件适配**：
   - 移除不支持的`depends_on.condition`
   - 调整健康检查语法
   - 修改网络配置

2. **脚本适配**：
   - 替换`docker`命令为`podman`
   - 替换`docker-compose`为`podman-compose`
   - 调整权限检查逻辑

3. **网络配置**：
   - 调整Coturn的网络模式
   - 配置端口映射

4. **权限管理**：
   - 配置无根容器支持
   - 调整文件权限

---

## 🖥️ 系统环境准备

### 支持的操作系统

- **推荐**: Ubuntu 20.04 LTS 或更新版本
- **支持**: Debian 11/12, CentOS 8+, Fedora 35+
- **架构**: x86_64 (amd64)

### 硬件要求

与Docker版本相同：
- **CPU**: 4核心（推荐8核心）
- **内存**: 8GB（推荐16GB）
- **存储**: 100GB SSD（推荐200GB以上）

### 第一步：更新系统

```bash
# 更新软件包列表
sudo apt update

# 升级已安装的软件包
sudo apt upgrade -y

# 安装基础工具包
sudo apt install -y curl wget git nano vim unzip htop iotop \
    dnsutils net-tools iputils-ping ufw fail2ban \
    build-essential python3 python3-pip

# 重启系统（推荐）
sudo reboot
```

### 第二步：创建专用用户

```bash
# 创建matrix用户
sudo useradd -m -s /bin/bash matrix

# 设置强密码
sudo passwd matrix

# 添加到必要的用户组
sudo usermod -aG sudo matrix

# 验证用户创建
id matrix
```

---

## 🐳 Podman安装和配置

### 安装Podman

#### Ubuntu/Debian系统

```bash
# 更新包列表
sudo apt update

# 安装Podman
sudo apt install -y podman

# 验证安装
podman --version
```

#### CentOS/RHEL/Fedora系统

```bash
# CentOS/RHEL
sudo dnf install -y podman

# 或者使用yum（较老版本）
sudo yum install -y podman

# Fedora
sudo dnf install -y podman

# 验证安装
podman --version
```

### 安装podman-compose

```bash
# 方法1：使用pip安装（推荐）
pip3 install --user podman-compose

# 确保用户级包路径在PATH中
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# 验证安装
podman-compose --version

# 方法2：使用包管理器（如果可用）
# Ubuntu 22.04+
sudo apt install -y podman-compose

# 方法3：从源码安装
git clone https://github.com/containers/podman-compose.git
cd podman-compose
sudo python3 setup.py install
```

### 配置Podman

#### 配置无根容器支持

```bash
# 切换到matrix用户
su - matrix

# 配置用户命名空间
echo "matrix:100000:65536" | sudo tee -a /etc/subuid
echo "matrix:100000:65536" | sudo tee -a /etc/subgid

# 验证配置
grep matrix /etc/subuid /etc/subgid
```

#### 配置Podman注册表

```bash
# 创建配置目录
mkdir -p ~/.config/containers

# 配置注册表
cat > ~/.config/containers/registries.conf << 'EOF'
# Podman注册表配置文件
# 配置容器镜像仓库

[registries.search]
# 搜索镜像时使用的注册表列表
registries = ['docker.io', 'quay.io']

[registries.insecure]
# 不安全的注册表（HTTP）
registries = []

[registries.block]
# 阻止的注册表
registries = []

# Docker Hub配置
[[registry]]
prefix = "docker.io"
location = "docker.io"

# Quay.io配置
[[registry]]
prefix = "quay.io"
location = "quay.io"
EOF
```

#### 配置存储

```bash
# 配置存储驱动
cat > ~/.config/containers/storage.conf << 'EOF'
# Podman存储配置文件

[storage]
# 存储驱动（overlay是推荐的驱动）
driver = "overlay"

# 存储根目录
graphroot = "/home/<USER>/.local/share/containers/storage"

# 运行时目录
runroot = "/run/user/1001/containers"

[storage.options]
# 存储选项
mount_program = "/usr/bin/fuse-overlayfs"

[storage.options.overlay]
# Overlay存储选项
mountopt = "nodev,metacopy=on"
EOF
```

### 测试Podman安装

```bash
# 测试基本功能
podman run --rm hello-world

# 测试镜像拉取
podman pull nginx:alpine

# 测试容器运行
podman run -d --name test-nginx -p 8080:80 nginx:alpine

# 检查运行状态
podman ps

# 清理测试容器
podman stop test-nginx
podman rm test-nginx
podman rmi nginx:alpine
```

---

## 📁 项目配置文件适配

### 创建Podman版本的Compose文件

```bash
# 切换到项目目录
cd /opt/matrix

# 创建Podman专用目录
mkdir -p podman/{config,scripts}

# 创建Podman版本的docker-compose.yml
cat > podman/docker-compose.yml << 'EOF'
# Matrix Homeserver Podman Compose配置
# 适配Podman的容器编排文件
#
# 主要修改：
# 1. 移除不支持的depends_on条件
# 2. 调整健康检查语法
# 3. 修改网络配置
# 4. 调整资源限制语法

version: '3.8'

services:
  # PostgreSQL数据库
  # 说明：Matrix Homeserver的主数据库，存储用户数据、房间信息、消息等
  db:
    image: postgres:15-alpine
    container_name: matrix_postgres
    restart: unless-stopped

    # 环境变量配置
    # 这些变量会从.env文件中读取
    environment:
      POSTGRES_DB: ${DB_NAME:-synapse}           # 数据库名称，默认为synapse
      POSTGRES_USER: ${DB_USER:-synapse}         # 数据库用户，默认为synapse
      POSTGRES_PASSWORD: ${DB_PASSWORD}          # 数据库密码，必须设置
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"  # 初始化参数

    # 数据卷挂载
    # 持久化数据库数据和初始化脚本
    volumes:
      - ./data/postgres:/var/lib/postgresql/data                    # 数据库数据目录
      - ./config/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql:ro  # 初始化脚本

    # 网络配置
    networks:
      - matrix_network

    # Podman资源限制语法
    # 注意：Podman的资源限制语法与Docker略有不同
    deploy:
      resources:
        limits:
          memory: ${POSTGRES_MEMORY_LIMIT:-1g}
          cpus: '${POSTGRES_CPU_LIMIT:-1}'

    # 健康检查（Podman兼容语法）
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-synapse} || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis缓存服务
  # 说明：用于缓存和会话存储，提高性能
  redis:
    image: redis:7-alpine
    container_name: matrix_redis
    restart: unless-stopped

    # Redis启动命令
    # 配置持久化、内存限制和淘汰策略
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

    # 数据卷挂载
    volumes:
      - ./data/redis:/data                       # Redis数据持久化目录

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT:-512m}
          cpus: '${REDIS_CPU_LIMIT:-0.5}'

    # 健康检查
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Synapse Matrix服务器
  # 说明：Matrix协议的核心实现，处理所有Matrix通信
  synapse:
    image: matrixdotorg/synapse:latest
    container_name: matrix_synapse
    restart: unless-stopped

    # 注意：Podman不完全支持depends_on条件，需要手动确保启动顺序
    # 启动前请确保db和redis服务已经运行

    # 环境变量配置
    environment:
      SYNAPSE_SERVER_NAME: ${DOMAIN}             # Matrix服务器域名
      SYNAPSE_REPORT_STATS: "no"                # 不发送统计信息
      SYNAPSE_CONFIG_PATH: /data/homeserver.yaml # 配置文件路径

    # 数据卷挂载
    volumes:
      - ./data/synapse:/data                     # Synapse数据目录
      - ./config/homeserver.yaml:/data/homeserver.yaml:ro  # 主配置文件
      - ./config/log.config:/data/log.config:ro # 日志配置文件

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${SYNAPSE_MEMORY_LIMIT:-2g}
          cpus: '${SYNAPSE_CPU_LIMIT:-2}'

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/_matrix/client/versions || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Coturn TURN/STUN服务器
  # 说明：用于音视频通话的NAT穿透服务
  # 注意：Podman中的网络配置与Docker略有不同
  coturn:
    image: coturn/coturn:latest
    container_name: matrix_coturn
    restart: unless-stopped

    # 网络配置：使用端口映射替代host模式
    # 这是因为Podman的host网络模式在无根容器中有限制
    ports:
      - "3478:3478/tcp"                          # STUN/TURN TCP端口
      - "3478:3478/udp"                          # STUN/TURN UDP端口
      - "5349:5349/tcp"                          # TURNS TCP端口
      - "49152-65535:49152-65535/udp"            # TURN UDP端口范围

    # 数据卷挂载
    volumes:
      - ./data/coturn/conf/turnserver.conf:/etc/coturn/turnserver.conf:ro  # 配置文件
      - ./data/coturn/certs:/etc/coturn/certs:ro                           # SSL证书
      - ./data/coturn/logs:/var/log/coturn                                 # 日志目录
      - /root/.acme.sh:/root/.acme.sh:ro                                   # acme.sh证书目录

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${COTURN_MEMORY_LIMIT:-512m}
          cpus: '${COTURN_CPU_LIMIT:-1}'

  # Nginx反向代理
  # 说明：处理HTTPS请求，转发到Synapse服务
  nginx:
    image: nginx:alpine
    container_name: matrix_nginx
    restart: unless-stopped

    # 端口映射
    # 绑定到本地接口，通过路由器端口转发对外提供服务
    ports:
      - "127.0.0.1:${HTTPS_PORT:-8448}:${HTTPS_PORT:-8448}"

    # 数据卷挂载
    volumes:
      - ./data/nginx/conf/nginx.conf:/etc/nginx/nginx.conf:ro              # Nginx主配置
      - ./data/nginx/conf/matrix.conf:/etc/nginx/conf.d/matrix.conf:ro     # Matrix站点配置
      - ./data/nginx/certs:/etc/nginx/certs:ro                             # SSL证书
      - ./data/nginx/logs:/var/log/nginx                                   # 日志目录
      - ./data/synapse/media:/var/www/matrix/media:ro                      # 媒体文件
      - /root/.acme.sh:/root/.acme.sh:ro                                   # acme.sh证书目录

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${NGINX_MEMORY_LIMIT:-256m}
          cpus: '${NGINX_CPU_LIMIT:-0.5}'

    # 健康检查
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

# 网络定义
# 创建自定义网络供容器间通信
networks:
  matrix_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷定义
# 定义命名卷用于数据持久化
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  synapse_data:
    driver: local
  nginx_logs:
    driver: local
  coturn_logs:
    driver: local
EOF

---

## 🔧 脚本文件适配

### 创建Podman版本的设置脚本

```bash
# 创建Podman适配的setup.sh脚本
cat > podman/scripts/setup.sh << 'EOF'
#!/bin/bash
# Matrix Homeserver Podman版本设置脚本
# 适配Podman容器引擎的自动化部署脚本
#
# 主要修改：
# 1. 替换docker命令为podman命令
# 2. 替换docker-compose为podman-compose
# 3. 调整权限检查逻辑
# 4. 修改服务启动方式

set -euo pipefail

# ================================================================
# 全局变量定义
# ================================================================

# 脚本基础信息
SCRIPT_NAME="Matrix Homeserver Podman Setup"
SCRIPT_VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 日志配置
LOG_LEVEL="${LOG_LEVEL:-INFO}"
LOG_FILE="${LOG_FILE:-/var/log/matrix/setup.log}"

# 颜色定义（用于终端输出）
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ================================================================
# 日志函数
# ================================================================

# 创建日志目录
setup_logging() {
    local log_dir
    log_dir="$(dirname "$LOG_FILE")"

    if [[ ! -d "$log_dir" ]]; then
        sudo mkdir -p "$log_dir"
        sudo chown "$(whoami):$(whoami)" "$log_dir"
    fi
}

# 日志输出函数
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 输出到日志文件
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"

    # 根据级别输出到终端
    case "$level" in
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" >&2
            ;;
        WARN)
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        INFO)
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        DEBUG)
            if [[ "$LOG_LEVEL" == "DEBUG" ]]; then
                echo -e "${BLUE}[DEBUG]${NC} $message"
            fi
            ;;
    esac
}

# 便捷日志函数
log_error() { log "ERROR" "$@"; }
log_warn() { log "WARN" "$@"; }
log_info() { log "INFO" "$@"; }
log_debug() { log "DEBUG" "$@"; }

# ================================================================
# 系统检查函数
# ================================================================

# 检查用户权限（Podman版本）
check_user_permissions() {
    log_info "检查用户权限"

    # 检查是否为root用户（不推荐）
    if [[ $EUID -eq 0 ]]; then
        log_warn "不建议以root用户运行此脚本"
        log_warn "Podman支持无根容器，建议使用普通用户"
    fi

    # 检查用户是否在必要的组中（Podman不需要docker组）
    log_info "当前用户: $(whoami)"
    log_info "用户组: $(groups)"

    # 检查subuid和subgid配置（无根容器必需）
    if ! grep -q "$(whoami)" /etc/subuid 2>/dev/null; then
        log_error "未配置用户命名空间，请运行以下命令："
        log_error "echo \"$(whoami):100000:65536\" | sudo tee -a /etc/subuid"
        log_error "echo \"$(whoami):100000:65536\" | sudo tee -a /etc/subgid"
        return 1
    fi

    return 0
}

# 检查系统依赖（Podman版本）
check_system_dependencies() {
    log_info "检查系统依赖"

    local required_commands=(
        "podman"                    # Podman容器引擎
        "podman-compose"           # Podman Compose工具
        "curl"                     # HTTP客户端
        "openssl"                  # SSL工具
        "acme.sh"                  # SSL证书管理
        "python3"                  # Python解释器
    )

    local missing_commands=()

    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_commands+=("$cmd")
            log_error "缺少命令: $cmd"
        else
            local version
            case "$cmd" in
                "podman")
                    version=$(podman --version | cut -d' ' -f3)
                    log_info "Podman版本: $version"
                    ;;
                "podman-compose")
                    version=$(podman-compose --version 2>/dev/null | cut -d' ' -f3 || echo "未知")
                    log_info "Podman Compose版本: $version"
                    ;;
                "acme.sh")
                    version=$(acme.sh --version 2>/dev/null | head -1 || echo "未知")
                    log_info "acme.sh版本: $version"
                    ;;
            esac
        fi
    done

    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_error "缺少必需的命令: ${missing_commands[*]}"
        log_info "请安装缺少的依赖后重新运行"
        return 1
    fi

    # 检查Podman服务状态（对于系统级Podman）
    if systemctl is-active --quiet podman.socket 2>/dev/null; then
        log_info "Podman socket服务运行正常"
    else
        log_info "Podman socket服务未运行（无根模式下正常）"
    fi

    return 0
}

# 检查网络连通性
check_network_connectivity() {
    log_info "检查网络连通性"

    local test_hosts=(
        "*******"                  # Google DNS
        "*******"                  # Cloudflare DNS
        "registry-1.docker.io"     # Docker Hub
    )

    for host in "${test_hosts[@]}"; do
        if ping -c 1 -W 3 "$host" >/dev/null 2>&1; then
            log_info "网络连通性正常: $host"
        else
            log_warn "无法连接到: $host"
        fi
    done

    return 0
}

# ================================================================
# 配置文件处理函数
# ================================================================

# 加载配置文件
load_configuration() {
    log_info "加载配置文件"

    local config_file="$PROJECT_DIR/config/deployment.env"

    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        log_info "请先复制并编辑配置模板:"
        log_info "cp $PROJECT_DIR/config/deployment.env.template $config_file"
        return 1
    fi

    # 加载配置文件
    # shellcheck source=/dev/null
    source "$config_file"

    # 验证必需的配置项
    local required_vars=(
        "DOMAIN"
        "SUBDOMAIN_MATRIX"
        "DB_PASSWORD"
        "CLOUDFLARE_API_TOKEN"
        "CLOUDFLARE_ZONE_ID"
    )

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "缺少必需的配置项: $var"
            return 1
        fi
    done

    log_info "配置文件加载完成"
    log_info "域名: $DOMAIN"
    log_info "Matrix子域名: $SUBDOMAIN_MATRIX"

    return 0
}
EOF

# 创建Podman版本的健康检查脚本
cat > podman/scripts/health_check.sh << 'EOF'
#!/bin/bash
# Matrix Homeserver Podman版本健康检查脚本
# 适配Podman容器引擎的服务监控脚本
#
# 主要修改：
# 1. 替换docker命令为podman命令
# 2. 调整容器检查逻辑
# 3. 修改服务重启方式

set -euo pipefail

# ================================================================
# 全局变量定义
# ================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/var/log/matrix/health_check.log"

# 健康检查结果
OVERALL_HEALTH="HEALTHY"
CHECK_RESULTS=()

# ================================================================
# 日志函数（复用setup.sh中的函数）
# ================================================================

source "$SCRIPT_DIR/setup.sh"

# ================================================================
# Podman服务检查函数
# ================================================================

# 检查Podman容器状态
check_podman_services() {
    log_info "检查Podman容器状态"

    local services=("matrix_postgres" "matrix_redis" "matrix_synapse" "matrix_nginx" "matrix_coturn")
    local healthy_count=0

    for service in "${services[@]}"; do
        # 使用podman ps检查容器状态
        if podman ps --format "{{.Names}}" | grep -q "^${service}$"; then
            # 检查容器健康状态
            local health_status
            health_status=$(podman inspect "$service" --format "{{.State.Health.Status}}" 2>/dev/null || echo "unknown")

            case "$health_status" in
                "healthy"|"unknown")
                    record_check_result "$service" "HEALTHY" "容器运行正常"
                    ((healthy_count++))
                    ;;
                "unhealthy")
                    record_check_result "$service" "CRITICAL" "容器健康检查失败"

                    if [[ "$FIX_ISSUES" == "true" ]]; then
                        log_info "尝试重启容器: $service"
                        podman restart "$service"
                    fi
                    ;;
                "starting")
                    record_check_result "$service" "WARNING" "容器正在启动"
                    ;;
            esac
        else
            record_check_result "$service" "CRITICAL" "容器未运行"

            if [[ "$FIX_ISSUES" == "true" ]]; then
                log_info "尝试启动容器: $service"
                cd "$PROJECT_DIR"
                podman-compose up -d "${service#matrix_}"
            fi
        fi
    done

    if [[ $healthy_count -eq ${#services[@]} ]]; then
        record_check_result "podman_services" "HEALTHY" "所有Podman容器运行正常 ($healthy_count/${#services[@]})"
    else
        record_check_result "podman_services" "CRITICAL" "部分Podman容器异常 ($healthy_count/${#services[@]})"
    fi
}

# 检查数据库连接（Podman版本）
check_database_connection() {
    log_info "检查数据库连接"

    # 使用podman exec检查PostgreSQL连接
    if podman exec matrix_postgres pg_isready -U "${DB_USER}" >/dev/null 2>&1; then
        record_check_result "database_connection" "HEALTHY" "数据库连接正常"

        # 检查数据库大小
        local db_size
        db_size=$(podman exec matrix_postgres \
            psql -U "${DB_USER}" -d "${DB_NAME}" -t -c "SELECT pg_size_pretty(pg_database_size('${DB_NAME}'));" 2>/dev/null | xargs || echo "未知")

        record_check_result "database_size" "HEALTHY" "数据库大小: $db_size"

        # 检查活跃连接数
        local active_connections
        active_connections=$(podman exec matrix_postgres \
            psql -U "${DB_USER}" -d "${DB_NAME}" -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null | xargs || echo "0")

        if [[ $active_connections -lt 50 ]]; then
            record_check_result "database_connections" "HEALTHY" "活跃连接数正常: $active_connections"
        else
            record_check_result "database_connections" "WARNING" "活跃连接数较高: $active_connections"
        fi
    else
        record_check_result "database_connection" "CRITICAL" "数据库连接失败"
    fi
}

# 检查Matrix服务API
check_matrix_api() {
    log_info "检查Matrix服务API"

    local matrix_url="https://${SUBDOMAIN_MATRIX}.${DOMAIN}:${HTTPS_PORT:-8448}"
    local api_endpoint="/_matrix/client/versions"

    # 检查API响应
    if curl -k -s --max-time 10 "${matrix_url}${api_endpoint}" >/dev/null 2>&1; then
        record_check_result "matrix_api" "HEALTHY" "Matrix API响应正常"

        # 检查联邦API
        local federation_endpoint="/_matrix/federation/v1/version"
        if curl -k -s --max-time 10 "${matrix_url}${federation_endpoint}" >/dev/null 2>&1; then
            record_check_result "matrix_federation" "HEALTHY" "Matrix联邦API正常"
        else
            record_check_result "matrix_federation" "WARNING" "Matrix联邦API异常"
        fi
    else
        record_check_result "matrix_api" "CRITICAL" "Matrix API无响应"
    fi
}

# 记录检查结果
record_check_result() {
    local component="$1"
    local status="$2"
    local message="$3"

    CHECK_RESULTS+=("$component:$status:$message")

    case "$status" in
        "CRITICAL")
            OVERALL_HEALTH="CRITICAL"
            log_error "[$component] $message"
            ;;
        "WARNING")
            if [[ "$OVERALL_HEALTH" != "CRITICAL" ]]; then
                OVERALL_HEALTH="WARNING"
            fi
            log_warn "[$component] $message"
            ;;
        "HEALTHY")
            log_info "[$component] $message"
            ;;
    esac
}

# ================================================================
# 主函数
# ================================================================

main() {
    log_info "开始Matrix Homeserver健康检查（Podman版本）"

    # 加载配置
    load_configuration

    # 执行检查
    check_podman_services
    check_database_connection
    check_matrix_api

    # 输出总结
    echo ""
    echo "=== 健康检查总结 ==="
    echo "总体状态: $OVERALL_HEALTH"
    echo ""

    for result in "${CHECK_RESULTS[@]}"; do
        IFS=':' read -r component status message <<< "$result"
        case "$status" in
            "HEALTHY")
                echo -e "${GREEN}✓${NC} $component: $message"
                ;;
            "WARNING")
                echo -e "${YELLOW}⚠${NC} $component: $message"
                ;;
            "CRITICAL")
                echo -e "${RED}✗${NC} $component: $message"
                ;;
        esac
    done

    # 提供建议
    if [[ "$OVERALL_HEALTH" == "CRITICAL" ]]; then
        echo ""
        echo "建议操作:"
        echo "  1. 检查容器状态: podman ps -a"
        echo "  2. 查看容器日志: podman logs <container_name>"
        echo "  3. 尝试自动修复: $0 --fix"
        echo ""
    fi

    log_info "健康检查完成，总体状态: $OVERALL_HEALTH"
}

# 解析命令行参数
FIX_ISSUES="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --fix)
            FIX_ISSUES="true"
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --fix    尝试自动修复发现的问题"
            echo "  --help   显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 设置日志
setup_logging

# 运行主函数
main
EOF

---

## 🚀 服务部署和配置

### 第一步：准备配置文件

```bash
# 切换到项目目录
cd /opt/matrix

# 复制配置模板
cp config/deployment.env.template podman/config/deployment.env

# 编辑配置文件
nano podman/config/deployment.env
```

**重要配置项（Podman版本特殊说明）**：

```bash
# ================================================================
# 容器引擎配置（Podman专用）
# ================================================================

# 容器引擎类型
CONTAINER_ENGINE="podman"                    # 使用Podman引擎
COMPOSE_COMMAND="podman-compose"             # 使用podman-compose

# 无根容器配置
ENABLE_ROOTLESS="true"                       # 启用无根容器
PODMAN_SOCKET_PATH="/run/user/$(id -u)/podman/podman.sock"  # Podman socket路径

# 网络配置（Podman特殊配置）
PODMAN_NETWORK_MODE="bridge"                 # 网络模式（bridge/host）
ENABLE_HOST_NETWORK="false"                  # 是否启用host网络（Coturn）

# 存储配置
PODMAN_STORAGE_DRIVER="overlay"              # 存储驱动
PODMAN_STORAGE_ROOT="/home/<USER>/.local/share/containers/storage"  # 存储根目录

# ================================================================
# 其他配置项与Docker版本相同
# ================================================================
```

### 第二步：初始化项目

```bash
# 进入Podman项目目录
cd /opt/matrix/podman

# 运行初始化脚本
chmod +x scripts/setup.sh
./scripts/setup.sh

# 如果需要强制重新初始化
./scripts/setup.sh --force
```

### 第三步：启动服务

```bash
# 拉取镜像
podman-compose pull

# 启动数据库和Redis（先启动依赖服务）
podman-compose up -d db redis

# 等待数据库启动完成
sleep 30

# 启动Synapse服务
podman-compose up -d synapse

# 等待Synapse启动完成
sleep 30

# 启动Nginx和Coturn
podman-compose up -d nginx coturn

# 查看服务状态
podman-compose ps
```

### 第四步：创建管理员用户

```bash
# 进入Synapse容器创建管理员用户
podman exec -it matrix_synapse register_new_matrix_user \
    -c /data/homeserver.yaml \
    http://localhost:8008

# 按提示输入用户名、密码和是否为管理员
```

### 第五步：验证部署

```bash
# 运行健康检查
chmod +x scripts/health_check.sh
./scripts/health_check.sh

# 测试Matrix API
curl -k https://matrix.example.com:8448/_matrix/client/versions

# 查看服务日志
podman-compose logs -f
```

---

## 🔧 故障排除指南

### 常见问题和解决方案

#### 1. Podman权限问题

**问题**：容器启动失败，提示权限错误

**解决方案**：
```bash
# 检查用户命名空间配置
grep $(whoami) /etc/subuid /etc/subgid

# 如果没有配置，添加配置
echo "$(whoami):100000:65536" | sudo tee -a /etc/subuid
echo "$(whoami):100000:65536" | sudo tee -a /etc/subgid

# 重新登录或重启用户会话
```

#### 2. 网络连接问题

**问题**：容器间无法通信

**解决方案**：
```bash
# 检查Podman网络
podman network ls

# 重新创建网络
podman network rm matrix_network
podman network create matrix_network

# 重启服务
podman-compose down
podman-compose up -d
```

#### 3. 端口映射问题

**问题**：无法访问服务端口

**解决方案**：
```bash
# 检查端口占用
sudo netstat -tlnp | grep :8448

# 检查防火墙设置
sudo ufw status

# 检查Podman端口映射
podman port matrix_nginx
```

#### 4. 存储权限问题

**问题**：数据卷挂载失败

**解决方案**：
```bash
# 检查目录权限
ls -la /opt/matrix/podman/data/

# 修复权限
sudo chown -R $(id -u):$(id -g) /opt/matrix/podman/data/

# 设置SELinux标签（如果启用SELinux）
sudo setsebool -P container_manage_cgroup on
```

#### 5. 服务依赖问题

**问题**：服务启动顺序错误

**解决方案**：
```bash
# 手动按顺序启动服务
podman-compose up -d db redis
sleep 30
podman-compose up -d synapse
sleep 30
podman-compose up -d nginx coturn

# 或使用启动脚本
cat > start_services.sh << 'EOF'
#!/bin/bash
echo "启动数据库服务..."
podman-compose up -d db redis
sleep 30

echo "启动Synapse服务..."
podman-compose up -d synapse
sleep 30

echo "启动代理服务..."
podman-compose up -d nginx coturn

echo "所有服务启动完成"
podman-compose ps
EOF

chmod +x start_services.sh
./start_services.sh
```

### 调试命令

```bash
# 查看容器状态
podman ps -a

# 查看容器日志
podman logs matrix_synapse
podman logs matrix_postgres
podman logs matrix_nginx

# 进入容器调试
podman exec -it matrix_synapse /bin/bash

# 检查容器资源使用
podman stats

# 检查网络连接
podman exec matrix_synapse netstat -tlnp
```

---

## 🔄 迁移步骤

### 从Docker迁移到Podman

如果您已经有Docker版本的Matrix Homeserver在运行，可以按以下步骤迁移：

#### 第一步：备份数据

```bash
# 停止Docker服务
docker-compose down

# 备份数据目录
sudo cp -r /opt/matrix/internal/data /opt/matrix/backup-$(date +%Y%m%d)

# 备份配置文件
sudo cp -r /opt/matrix/internal/config /opt/matrix/backup-config-$(date +%Y%m%d)
```

#### 第二步：安装Podman

```bash
# 按照前面的步骤安装Podman和podman-compose
```

#### 第三步：迁移配置

```bash
# 复制数据到Podman目录
cp -r /opt/matrix/internal/data /opt/matrix/podman/
cp -r /opt/matrix/internal/config /opt/matrix/podman/

# 调整文件权限
sudo chown -R $(id -u):$(id -g) /opt/matrix/podman/data/
sudo chown -R $(id -u):$(id -g) /opt/matrix/podman/config/
```

#### 第四步：启动Podman服务

```bash
# 使用Podman启动服务
cd /opt/matrix/podman
podman-compose up -d

# 验证迁移成功
./scripts/health_check.sh
```

#### 第五步：清理Docker环境（可选）

```bash
# 确认Podman版本正常运行后，可以清理Docker环境
docker system prune -a
sudo apt remove docker-ce docker-ce-cli containerd.io
```

---

## 📝 总结

### Podman版本的优势

1. **更好的安全性**：无根容器支持，减少安全风险
2. **无守护进程**：系统资源占用更少
3. **兼容性**：与Docker命令高度兼容
4. **标准化**：更符合OCI标准

### 注意事项

1. **学习曲线**：需要了解Podman特有的概念和配置
2. **生态系统**：某些Docker工具可能不完全兼容
3. **调试**：调试方法与Docker略有不同

### 推荐使用场景

- 对安全性要求较高的环境
- 希望减少系统资源占用
- 需要无根容器支持
- 符合企业安全策略要求

通过本指南，您应该能够成功地使用Podman部署Matrix Homeserver，享受更安全、更高效的容器化体验。
```
